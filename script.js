// Countdown Timer for Pioneer Program
function updateCountdown() {
    const now = new Date().getTime();
    const targetDate = new Date('2024-10-15').getTime(); // October 15th as target
    const timeLeft = targetDate - now;

    if (timeLeft > 0) {
        const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
        const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

        const countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            if (days > 0) {
                countdownElement.textContent = `${days} days, ${hours} hours remaining`;
            } else if (hours > 0) {
                countdownElement.textContent = `${hours} hours, ${minutes} minutes remaining`;
            } else {
                countdownElement.textContent = `${minutes} minutes remaining`;
            }
        }
    } else {
        const countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            countdownElement.textContent = 'Limited time remaining!';
        }
    }
}

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-link, .btn-primary[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href && href.startsWith('#')) {
                e.preventDefault();
                const targetId = href;
                const targetSection = document.querySelector(targetId);
                
                if (targetSection) {
                    const offsetTop = targetSection.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
}

// Intersection Observer for animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.problem-card, .feature-item, .benefit-item, .timeline-item');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Case Study Dashboard Tabs
function initCaseStudyTabs() {
    const tabs = document.querySelectorAll('.dashboard-tabs .tab');
    const beforeMetrics = document.querySelectorAll('.metric-item.before');
    const afterMetrics = document.querySelectorAll('.metric-item.after');

    // Default: show "Before", hide "After"
    beforeMetrics.forEach(m => m.style.display = 'block');
    afterMetrics.forEach(m => m.style.display = 'none');

    tabs.forEach((tab, index) => {
        tab.addEventListener('click', () => {
            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            // Show/hide metrics based on tab
            if (index === 0) { // Before tab
                beforeMetrics.forEach(metric => {
                    metric.style.display = 'block';
                    metric.style.animation = 'fadeInUp 0.6s ease-out';
                });
                afterMetrics.forEach(metric => {
                    metric.style.display = 'none';
                });
            } else { // After tab
                beforeMetrics.forEach(metric => {
                    metric.style.display = 'none';
                });
                afterMetrics.forEach(metric => {
                    metric.style.display = 'block';
                    metric.style.animation = 'fadeInUp 0.6s ease-out';
                });
            }
        });
    });
}

// Navbar scroll effect
function initNavbarScroll() {
    const navbar = document.querySelector('.navbar');
    let lastScrollTop = 0;

    window.addEventListener('scroll', () => {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }

        lastScrollTop = scrollTop;
    });
}

// Chart animations
function initChartAnimations() {
    const chartBars = document.querySelectorAll('.chart-bar');
    
    chartBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.height = bar.style.height || '60%';
            bar.style.animation = 'growBar 1.5s ease-out';
        }, index * 200);
    });
}

// Typing effect for hero title (disabled to prevent raw HTML rendering)
function initTypingEffect() {
    // Intentionally left unused. If re-enabled, ensure it skips HTML tags.
}

// Parallax effect for hero section
function initParallaxEffect() {
    const heroSection = document.querySelector('.hero');
    const dashboardMockup = document.querySelector('.dashboard-mockup');

    if (!heroSection || !dashboardMockup) return;

    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.1;
        dashboardMockup.style.transform = `perspective(1000px) rotateY(-5deg) rotateX(5deg) translateY(${rate}px)`;
    });
}

// Progress bar for timeline
function initTimelineProgress() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    const timelineContainer = document.querySelector('.timeline-container');

    if (!timelineContainer) return;

    const updateProgress = () => {
        const scrollTop = window.pageYOffset;
        const timelineTop = timelineContainer.offsetTop;
        const timelineHeight = timelineContainer.offsetHeight;
        const windowHeight = window.innerHeight;

        if (scrollTop + windowHeight > timelineTop && scrollTop < timelineTop + timelineHeight) {
            const progress = (scrollTop + windowHeight - timelineTop) / (timelineHeight + windowHeight);
            timelineItems.forEach((item, index) => {
                if (progress > index * 0.25) {
                    item.classList.add('completed');
                }
            });
        }
    };

    window.addEventListener('scroll', updateProgress);
}

// FOMO urgency effect
function initFOMOEffect() {
    const urgencyBanner = document.querySelector('.urgency-banner');
    if (!urgencyBanner) return;

    // Random urgency messages
    const urgencyMessages = [
        'Only 15 spots left!',
        'Limited Pioneer Spots Available',
        'Join the exclusive group!',
        'Early access closing soon!'
    ];

    let messageIndex = 0;
    setInterval(() => {
        const span = urgencyBanner.querySelector('span');
        if (span) {
            span.textContent = urgencyMessages[messageIndex];
            messageIndex = (messageIndex + 1) % urgencyMessages.length;
        }
    }, 3000);
}

// Form tracking for analytics
function initFormTracking() {
    const formLinks = document.querySelectorAll('a[href*="forms.gle"]');
    formLinks.forEach(link => {
        link.addEventListener('click', function() {
            // Track form clicks for analytics
            if (typeof gtag !== 'undefined') {
                gtag('event', 'form_click', {
                    'event_category': 'engagement',
                    'event_label': 'pioneer_program_application'
                });
            }
        });
    });
}

// Mobile menu toggle
function initMobileMenu() {
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    
    if (mobileToggle && navMenu) {
        mobileToggle.addEventListener('click', () => {
            mobileToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
        
        // Close menu when clicking on a link
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!mobileToggle.contains(e.target) && !navMenu.contains(e.target)) {
                mobileToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    }
}

// Performance optimization - lazy loading
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// Analytics tracking
function initAnalytics() {
    // Track page view
    if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_MEASUREMENT_ID', {
            'page_title': 'Nexaan Pioneer Program Landing Page',
            'page_location': window.location.href
        });
    }

    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', () => {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
        }
    });

    // Track time on page
    let timeSpent = 0;
    setInterval(() => {
        timeSpent += 1;
    }, 1000);

    // Track when user leaves page
    window.addEventListener('beforeunload', () => {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'timing_complete', {
                'name': 'time_on_page',
                'value': timeSpent
            });
        }
    });
}

// Error monitoring
function initErrorMonitoring() {
    window.addEventListener('error', function(e) {
        // Log errors for monitoring
        if (typeof gtag !== 'undefined') {
            gtag('event', 'exception', {
                'description': e.error?.message || 'Unknown error',
                'fatal': false
            });
        }
    });
}

// Performance monitoring
function initPerformanceMonitoring() {
    if ('performance' in window) {
        window.addEventListener('load', () => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (typeof gtag !== 'undefined') {
                gtag('event', 'timing_complete', {
                    'name': 'page_load_time',
                    'value': loadTime
                });
            }
        });
    }
}

// Scroll to top functionality
function initScrollToTop() {
    const scrollButton = document.getElementById('scroll-to-top');
    
    if (scrollButton) {
        // Show/hide button based on scroll position
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollButton.classList.add('visible');
            } else {
                scrollButton.classList.remove('visible');
            }
        });
        
        // Scroll to top when clicked
        scrollButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
}

// Initialize all functions when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading screen after page loads
    const loadingScreen = document.getElementById('loading-screen');
    if (loadingScreen) {
        setTimeout(() => {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }, 1200);
    }

    // Initialize all functionality
    updateCountdown();
    setInterval(updateCountdown, 60000); // Update every minute
    
    initSmoothScrolling();
    initScrollAnimations();
    initCaseStudyTabs();
    initNavbarScroll();
    initChartAnimations();
    // initTypingEffect(); // Disabled to prevent HTML tags showing during typing effect
    initParallaxEffect();
    initTimelineProgress();
    initFOMOEffect();
    initFormTracking();
    initMobileMenu();
    initLazyLoading();
    initAnalytics();
    initErrorMonitoring();
    initPerformanceMonitoring();
    initScrollToTop();

    // Smooth page reveal
    document.body.style.opacity = '0';
    document.body.style.transition = 'opacity 0.8s ease';
    setTimeout(() => { 
        document.body.style.opacity = '1'; 
    }, 200);
});

// Handle window resize
window.addEventListener('resize', () => {
    // Recalculate any layout-dependent elements
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach(bar => {
        bar.style.animation = 'none';
        setTimeout(() => {
            bar.style.animation = 'growBar 1.5s ease-out';
        }, 10);
    });
});
