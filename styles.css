/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-blue: #46AEE3;
    --primary-green: #007049;
    --secondary-green: #00d649;
    --neutral-gray: #7c878a;
    --accent-orange: #bf6f16;
    --background-light: #f9f9f9;
    --background-dark: #222222;
    --text-dark: #2C3E50;
    --text-light: #6C757D;
    --white: #FFFFFF;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-green));
    --gradient-secondary: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
--gradient-nexaan: linear-gradient(135deg, #7c878a, #007049, #bf6f16);
    --gradient-accent: linear-gradient(135deg, var(--accent-orange), #D2691E);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Montserrat', 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Nexaan brand styling with custom x */
.nexaan-brand {
    font-weight: 700;
    font-size: 1.1em;
    position: relative;
    display: inline-block;
}

.nexaan-brand::before {
    content: attr(data-text);
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-nexaan);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    z-index: -1;
}

/* Custom styling for the letter 'x' in Nexaan */
.nexaan-brand .nexaan-x {
    background: linear-gradient(90deg, #007049 50%, #bf6f16 50%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

/* Accent orange styling */
.accent-orange {
    color: var(--accent-orange);
    font-weight: 600;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3rem;
}

/* Logo sizing and cropping */
.nav-logo .logo {
	height: 40px; /* reduced for better balance */
	width: auto;
	object-fit: contain;
	object-position: left center;
	image-rendering: -webkit-optimize-contrast;
}

.nav-menu {
    display: flex;
    gap: 2.5rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    flex: 1;
}

.nav-link {
    text-decoration: none;
    color: #1e2a38;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: linear-gradient(145deg, #ffffff, #f0f0f0);
    box-shadow: 5px 5px 10px #d1d1d1, -5px -5px 10px #ffffff;
    transform: translateY(0);
}

.nav-link:hover {
    color: #007049;
    transform: translateY(-2px);
    box-shadow: 8px 8px 15px #d1d1d1, -8px -8px 15px #ffffff;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
}

.nav-link:active {
    transform: translateY(0);
    box-shadow: inset 5px 5px 10px #d1d1d1, inset -5px -5px 10px #ffffff;
}

.nav-cta .btn-primary {
    background: var(--gradient-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.nav-cta .btn-primary:hover {
    transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    transition: all 0.3s ease;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-content {
    text-align: center;
}

.loading-logo .logo {
    height: 80px;
    width: auto;
    margin-bottom: 2rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: radial-gradient(1200px 600px at 20% 10%, rgba(70,174,227,0.35), transparent 60%),
			   radial-gradient(900px 500px at 80% 20%, rgba(0,112,73,0.35), transparent 60%),
			   radial-gradient(800px 600px at 60% 90%, rgba(191,111,22,0.25), transparent 60%),
			   #eaf3f7;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 100%);
    opacity: 0.1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(0,0,0,0.15) 0%, rgba(0,0,0,0.08) 40%, rgba(0,0,0,0.0) 100%);
}

.hero-container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

/* Ensure hero text is dark-on-light for better readability */
.hero-content {
	color: #0f1b2a;
}

.hero-badge {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.badge {
    background: var(--gradient-accent);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Adjust badge timer color for readability */
.badge-timer {
	color: #1f2d3d;
	font-size: 0.9rem;
	font-weight: 500;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: #0f1b2a;
}

/* Reduce huge hero heading size slightly on large screens for balance */
@media (min-width: 1200px) {
	.hero-title { font-size: 3rem; }
}

/* Highlight text gradient with higher contrast */
.hero-title .highlight {
	background: linear-gradient(135deg, #007049, #46AEE3 50%, #bf6f16);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2.5rem;
    color: #1f2d3d;
    opacity: 0.9;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem 2rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-large {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
}

/* Make CTA note readable on light background */
.cta-note {
	font-size: 0.9rem;
	color: #4a5868;
	opacity: 1;
	text-align: left;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image {
    width: 100%;
    max-width: 500px;
}

.dashboard-mockup {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
    transition: transform 0.3s ease;
}

.dashboard-mockup:hover {
    transform: perspective(1000px) rotateY(-2deg) rotateX(2deg);
}

.dashboard-header {
    background: var(--background-dark);
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-dots {
    display: flex;
    gap: 0.5rem;
}

.dashboard-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--neutral-gray);
}

.dashboard-dots span:nth-child(1) { background: #ff5f56; }
.dashboard-dots span:nth-child(2) { background: #ffbd2e; }
.dashboard-dots span:nth-child(3) { background: #27ca3f; }

.dashboard-content {
    padding: 2rem;
}

.chart-container {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 120px;
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.chart-bar {
    flex: 1;
    background: var(--gradient-primary);
    border-radius: 4px 4px 0 0;
    transition: height 0.3s ease;
}

.metric-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.metric-card {
    text-align: center;
    padding: 1rem;
    background: var(--background-light);
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 0.5rem;
}

/* Improve card text contrast */
.problem-card p,
.feature-content p,
.result-item,
.metric-label { color: #4a5868; }

/* Case study metric labels contrast */
.metric-item p { color: #4a5868; }

.metric-label {
    font-size: 0.8rem;
    color: var(--text-light);
    line-height: 1.3;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

/* Improve section header contrast */
.section-header h2 { color: #0f1b2a; }
.section-header p { color: #4a5868; }

/* Problems Section */
.problems {
    padding: 6rem 0;
    background: var(--white);
}

.problems-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.problem-card {
    background: var(--background-light);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid #e9ecef;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.problem-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.problem-image {
    width: 100%;
    height: 200px;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.problem-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.problem-card:hover .problem-image img {
    transform: scale(1.05);
}

.problem-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.problem-card p {
    color: #4a5868;
    line-height: 1.6;
}

/* About Nexaan Section */
.nexaan {
    padding: 6rem 0;
    background: var(--background-light);
}

.nexaan-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.feature-item {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 1.2rem;
    color: white;
}

.feature-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.feature-content p {
    color: #4a5868;
    line-height: 1.6;
}

/* Core Modules Section */
.modules {
    padding: 6rem 0;
    background: var(--background-dark);
    color: var(--white);
}

.modules .section-header h2,
.modules .section-header p {
    color: var(--white);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2rem;
}

.module-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, background 0.3s ease;
}

.module-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.module-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.module-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-icon i {
    font-size: 1.2rem;
    color: white;
}

.module-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--white);
}

.module-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-style: italic;
    margin-bottom: 1.5rem;
}

.module-features {
    list-style: none;
    margin-bottom: 1.5rem;
}

.module-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: rgba(255, 255, 255, 0.9);
}

.module-features li i {
    color: var(--secondary-green);
    width: 16px;
}

.module-insight {
    background: rgba(191, 111, 22, 0.2);
    border-left: 4px solid var(--accent-orange);
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.module-insight i {
    color: var(--accent-orange);
    font-size: 1.1rem;
}

.module-insight span {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    font-style: italic;
}

/* Case Study Section */
.case-study {
    padding: 6rem 0;
    background: var(--white);
}

.case-study-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.dashboard-container {
    background: var(--white);
    border-radius: 15px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.dashboard-header {
    background: var(--background-dark);
    color: var(--white);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.dashboard-tabs {
    display: flex;
    gap: 1rem;
}

.tab {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.tab.active {
    background: var(--primary-blue);
    color: white;
}

.dashboard-body {
    padding: 2rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-item {
    text-align: center;
    position: relative;
}

.metric-item.before .metric-circle {
    background: #ff6b6b;
    color: white;
}

.metric-item.after .metric-circle {
    background: var(--primary-green);
    color: white;
}

.metric-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-size: 1.2rem;
    font-weight: 700;
}

.metric-item p {
    font-size: 0.8rem;
    color: #4a5868;
    margin-bottom: 0.5rem;
}

.metric-item i {
    font-size: 1.2rem;
}

.metric-item.before i {
    color: #ff6b6b;
}

.metric-item.after i {
    color: var(--primary-green);
}

.results-summary {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.result-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--background-light);
    border-radius: 8px;
    font-size: 0.9rem;
}

.result-item i {
    color: var(--primary-green);
    font-size: 1rem;
}

.case-study-person {
    display: flex;
    justify-content: center;
    align-items: center;
}

.person-container {
    text-align: center;
    position: relative;
}

.person-avatar {
    width: 120px;
    height: 120px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    position: relative;
}

.person-avatar i {
    font-size: 3rem;
    color: white;
}

.person-reaction {
    position: absolute;
    top: -20px;
    right: -20px;
}

.hands-up {
    width: 60px;
    height: 60px;
    background: var(--accent-orange);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
}

.hands-up i {
    font-size: 1.5rem;
    color: white;
}

.excitement-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.excitement-text span:first-child {
    font-size: 1.5rem;
}

.excitement-text span:last-child {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 600;
}

/* Timeline Section */
.timeline {
    padding: 6rem 0;
    background: var(--background-light);
}

.timeline-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
}

.timeline-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--neutral-gray);
    transform: translateY(-50%);
    z-index: 1;
}

.timeline-item {
    position: relative;
    z-index: 2;
    text-align: center;
    flex: 1;
}

.timeline-marker {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--white);
    border: 3px solid var(--neutral-gray);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

.timeline-item.completed .timeline-marker {
    background: var(--primary-green);
    border-color: var(--primary-green);
}

.timeline-item.current .timeline-marker {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.timeline-marker i {
    color: white;
    font-size: 1.2rem;
}

.timeline-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.timeline-content p {
    font-size: 0.9rem;
    color: var(--text-light);
    line-height: 1.4;
}

.current-badge {
    display: inline-block;
    background: var(--accent-orange);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

/* Pioneer Program Section */
.pioneer {
    padding: 6rem 0;
    background: var(--background-light);
}

.pioneer .section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.pioneer-content {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.urgency-banner {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    background: var(--gradient-accent);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.pioneer-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
}

.pioneer-intro {
    font-size: 1.2rem;
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 2.5rem;
}

.pioneer-benefits {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.pioneer-benefits h3 {
    color: var(--text-dark);
    margin-bottom: 2rem;
    font-size: 1.4rem;
    text-align: center;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.benefit-item {
    text-align: center;
    padding: 1.5rem;
    border-radius: 12px;
    transition: transform 0.3s ease;
    border: 1px solid #e9ecef;
}

.benefit-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.benefit-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.benefit-icon i {
    font-size: 1.2rem;
    color: white;
}

.benefit-item h4 {
    color: var(--text-dark);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.benefit-item p {
    color: var(--text-light);
    font-size: 0.9rem;
    line-height: 1.5;
}

.pioneer-requirements {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    text-align: center;
}

.pioneer-requirements h3 {
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
}

.requirements h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.pioneer-requirements ul {
    list-style: none;
    padding: 0;
    display: inline-block;
    text-align: left;
}

.pioneer-requirements li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: var(--text-light);
    font-size: 1rem;
}

.pioneer-requirements li i {
    color: var(--primary-green);
    font-size: 1.1rem;
}

.pioneer-cta {
    background: var(--gradient-primary);
    padding: 3rem;
    border-radius: 16px;
    text-align: center;
    color: white;
}

.cta-container h3 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.cta-container p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
    opacity: 0.9;
}

.cta-container .btn-primary {
    background: white;
    color: var(--primary-blue);
    font-size: 1.1rem;
    padding: 1rem 2rem;
    margin-bottom: 1rem;
}

.cta-container .btn-primary:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
}

.cta-note {
    font-size: 0.9rem;
    opacity: 0.8;
    font-style: italic;
}



/* Footer */
.footer {
    background: var(--background-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

/* Footer logo a bit larger for visual balance */
.footer-logo .logo {
	height: 60px;
	width: auto;
	object-fit: contain;
}

.footer-logo p {
    color: rgba(255, 255, 255, 0.8);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-section a,
.footer-section span {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-blue);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .nav-menu.active {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        padding: 1rem;
        box-shadow: var(--shadow);
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
    
    .problems-grid {
        grid-template-columns: 1fr;
    }
    
    .nexaan-features {
        grid-template-columns: 1fr;
    }
    
    .modules-grid {
        grid-template-columns: 1fr;
    }
    
    .case-study-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .results-summary {
        grid-template-columns: 1fr;
    }
    
    .timeline-container {
        flex-direction: column;
        gap: 2rem;
    }
    
    .timeline-container::before {
        display: none;
    }
    
    .pioneer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-links {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .section-header h2 {
        font-size: 1.8rem;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .btn-large {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    /* Compact mini strategy flow (Library → Strategy → Bundling → CMMS) */
    .mini-flow { grid-template-columns: 1fr; row-gap: 0.5rem; }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-content,
.problem-card,
.feature-item,
.benefit-item {
    animation: fadeInUp 0.6s ease-out;
}

/* Hover Effects */
.problem-card:hover .problem-icon,
.benefit-item:hover .benefit-icon {
    transform: scale(1.1);
}

/* Focus States for Accessibility */
.btn-primary:focus,
.nav-link:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Hero benefit panel */
.benefit-panel { padding: 1rem; }
.benefit-cards-hero {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.75rem;
}
.benefit-card {
	background: #ffffff;
	border: 1px solid #e9ecef;
	border-radius: 12px;
	padding: 0.9rem 0.8rem;
	text-align: center;
	box-shadow: 0 8px 18px rgba(0,0,0,0.08);
}
.benefit-icon {
	width: 36px;
	height: 36px;
	margin: 0 auto 0.5rem;
	border-radius: 8px;
	background: var(--gradient-primary);
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
}
.benefit-icon i { font-size: 1rem; }
.benefit-icon.green { background: var(--gradient-secondary); }
.benefit-icon.orange { background: var(--gradient-accent); }
.benefit-metric { font-size: 1.25rem; font-weight: 800; color: #0f1b2a; }
.benefit-label { font-size: 0.8rem; color: #4a5868; font-weight: 600; }

@media (max-width: 768px) {
	.benefit-cards-hero { grid-template-columns: 1fr; }
}

/* Modules subtle process flow */
.modules-flow {
	display: grid;
	grid-template-columns: repeat(11, minmax(0, 1fr));
	align-items: center;
	gap: 0.5rem;
	margin: 1.5rem 0 2.5rem;
}
.modules-flow .flow-step {
	background: rgba(255,255,255,0.12);
	border: 1px solid rgba(255,255,255,0.25);
	border-radius: 12px;
	padding: 0.75rem 0.9rem;
	text-align: center;
}
.modules-flow .flow-step i {
	background: rgba(255,255,255,0.18);
	color: #fff;
	width: 32px; height: 32px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center;
	margin-bottom: 0.4rem; font-size: 0.9rem;
}
.modules-flow .flow-step span { display: block; font-weight: 700; font-size: 0.9rem; }
.modules-flow .flow-step small { display: block; font-size: 0.75rem; opacity: .85; }
.modules-flow .flow-step.bidir { box-shadow: inset 0 0 0 2px rgba(70,174,227,.35); }
.modules-flow .flow-step.dim { opacity: .85; }
.modules-flow .flow-connector-arrow { height: 4px; background: linear-gradient(90deg, #46AEE3, #00d649); border-radius: 2px; }
.modules-flow .flow-routes { grid-column: 1 / -1; position: relative; height: 12px; }
.modules-flow .route.corporate-to-bundling { position: absolute; left: 10%; right: 32%; top: 6px; height: 2px; background: linear-gradient(90deg, #bf6f16, #46AEE3); opacity: .5; border-radius: 2px; }

/* Case study consolidated comparison */
.comparison-grid {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	gap: 1rem;
	margin-top: 2rem;
}
.comparison-card {
	background: #fff;
	border: 1px solid #e9ecef;
	border-radius: 12px;
	padding: 1.25rem;
	box-shadow: 0 10px 22px rgba(0,0,0,0.08);
}
.comparison-card h3 {
	margin-bottom: 1rem;
	font-size: 1.1rem;
	text-align: center;
}
.comparison-card.labels h3 { color: #0f1b2a; }
.comparison-card.before h3 { color: #a33; }
.comparison-card.after h3 { color: #0a6a49; }
.metric-row {
	display: flex;
	align-items: center;
	padding: 0.4rem 0;
	justify-content: center;
}
.comparison-card.labels .metric-row { justify-content: flex-start; }
.metric-row .value {
	font-weight: 800;
	color: #0f1b2a;
	background: #f3f7fb;
	padding: 0.25rem 0.5rem;
	border-radius: 8px;
	text-align: center;
	min-width: 80px;
}
.metric-row .label { 
	color: #4a5868; 
	font-weight: 600; 
	text-align: left;
}
@media (max-width: 768px) {
	.modules-grid { grid-template-columns: 1fr; }
	.comparison-grid { grid-template-columns: 1fr; }
}

/* Roadmap 3D-like */
.roadmap { padding: 6rem 0; background: var(--background-light); }
.road { position: relative; height: 220px; max-width: 1000px; margin: 0 auto; perspective: 800px; }
.road-line { position: absolute; top: 50%; left: 0; right: 0; height: 10px; background: linear-gradient(180deg, #cfd9df, #e2ebf0); border-radius: 6px; transform: rotateX(12deg); box-shadow: inset 0 2px 6px rgba(0,0,0,0.08); }
.road-node { position: absolute; top: calc(50% - 36px); left: var(--pos); transform: translateX(-50%); text-align: center; }
.node-icon { width: 48px; height: 48px; border-radius: 12px; background: var(--gradient-primary); color: #fff; display: flex; align-items: center; justify-content: center; margin: 0 auto 0.5rem; box-shadow: 0 10px 18px rgba(0,0,0,0.15); }
.node-label { display: flex; flex-direction: column; gap: 2px; }
.node-label strong { color: #0f1b2a; font-size: 0.95rem; }
.node-label span { color: #4a5868; font-size: 0.8rem; font-weight: 600; }
.road-node.done .node-icon { background: var(--gradient-secondary); }
.road-node.current .node-icon { background: var(--gradient-accent); }
@media (max-width: 768px) { .road { height: 320px; } .road-node { top: auto; bottom: 20px; } }
